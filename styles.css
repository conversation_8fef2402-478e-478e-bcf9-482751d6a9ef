/* CSS Variables for Elegant Theming */
:root {
    --primary-gold: #C9A961;
    --accent-gold: #E8D5A3;
    --dark-gold: #A08B47;
    --deep-brown: #2C1810;
    --warm-cream: #FDF8F0;
    --soft-beige: #F5F0E8;
    --elegant-grey: #8B7D6B;
    --text-dark: #2C1810;
    --text-medium: #5D4E37;
    --text-light: #8B7D6B;
    --shadow-soft: rgba(44, 24, 16, 0.08);
    --shadow-medium: rgba(44, 24, 16, 0.12);
    --shadow-strong: rgba(44, 24, 16, 0.2);
    --border-radius: 12px;
    --border-radius-lg: 20px;
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2.5rem;
    --spacing-xl: 4rem;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Amiri', sans-serif;
    line-height: 1.7;
    color: var(--text-dark);
    background: linear-gradient(135deg, var(--warm-cream) 0%, var(--soft-beige) 100%);
    min-height: 100vh;
    position: relative;
    font-weight: 400;
}

/* Elegant Moroccan Pattern Overlay */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 20%, var(--accent-gold) 1px, transparent 1px),
        radial-gradient(circle at 80% 80%, var(--accent-gold) 1px, transparent 1px),
        linear-gradient(45deg, transparent 48%, var(--accent-gold) 49%, var(--accent-gold) 51%, transparent 52%);
    background-size: 60px 60px, 60px 60px, 120px 120px;
    background-position: 0 0, 30px 30px, 0 0;
    opacity: 0.06;
    pointer-events: none;
    z-index: -1;
}

/* Main Container */
.menu-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-xl);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow:
        0 20px 60px var(--shadow-soft),
        0 0 0 1px rgba(201, 169, 97, 0.1);
    border-radius: var(--border-radius-lg);
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    position: relative;
    overflow: hidden;
}

.menu-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-gold), var(--accent-gold), var(--primary-gold));
}

/* Header Styles */
.menu-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg) 0 var(--spacing-xl) 0;
    position: relative;
    background: linear-gradient(135deg, rgba(201, 169, 97, 0.05), rgba(232, 213, 163, 0.05));
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-xl);
}

.menu-header::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--primary-gold), transparent);
}

.logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

.hotel-logo {
    width: 140px;
    height: auto;
    max-height: 140px;
    object-fit: contain;
    filter: drop-shadow(0 8px 16px var(--shadow-medium));
    transition: all 0.4s ease;
}

.logo-placeholder:hover .hotel-logo {
    transform: scale(1.08) rotate(2deg);
}

.hotel-name {
    font-family: 'Playfair Display', 'Amiri', serif;
    font-size: 4rem;
    font-weight: 700;
    color: var(--deep-brown);
    text-shadow: 0 2px 8px var(--shadow-soft);
    margin: 0;
    letter-spacing: 3px;
    position: relative;
}

.hotel-name::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: var(--primary-gold);
    border-radius: 2px;
}

.hotel-subtitle {
    font-size: 1.4rem;
    color: var(--text-medium);
    font-style: italic;
    font-weight: 300;
    letter-spacing: 1px;
    margin-top: var(--spacing-sm);
}

/* Section Styles */
.menu-section {
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-xl) var(--spacing-lg);
    background: rgba(255, 255, 255, 0.4);
    border-radius: var(--border-radius-lg);
    position: relative;
    backdrop-filter: blur(5px);
}

.menu-section:not(:last-child) {
    margin-bottom: var(--spacing-xl);
}

.menu-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--accent-gold), transparent);
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    position: relative;
}

.section-title {
    font-family: 'Playfair Display', 'Amiri', serif;
    font-size: 3rem;
    color: var(--deep-brown);
    margin-bottom: var(--spacing-md);
    position: relative;
    letter-spacing: 1px;
    font-weight: 600;
}

.section-title::before {
    content: '✦';
    position: absolute;
    left: -40px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-gold);
    font-size: 1.5rem;
}

.section-title::after {
    content: '✦';
    position: absolute;
    right: -40px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-gold);
    font-size: 1.5rem;
}

.decorative-divider {
    width: 150px;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--primary-gold), var(--accent-gold), var(--primary-gold), transparent);
    margin: 0 auto;
    position: relative;
    border-radius: 1px;
}

.decorative-divider::before,
.decorative-divider::after {
    content: '';
    position: absolute;
    top: -3px;
    width: 8px;
    height: 8px;
    background: radial-gradient(circle, var(--primary-gold), var(--dark-gold));
    border-radius: 50%;
    box-shadow: 0 2px 4px var(--shadow-soft);
}

.decorative-divider::before {
    left: -4px;
}

.decorative-divider::after {
    right: -4px;
}

/* Section Content Layout */
.section-content {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: var(--spacing-lg);
    align-items: start;
}

.section-content:nth-child(even) {
    grid-template-columns: 300px 1fr;
}

/* Menu Items */
.menu-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.menu-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.8);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 20px var(--shadow-soft);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(201, 169, 97, 0.1);
    position: relative;
    overflow: hidden;
}

.menu-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--primary-gold), var(--accent-gold));
    transform: scaleY(0);
    transition: transform 0.4s ease;
    transform-origin: bottom;
}

.menu-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px var(--shadow-medium);
    background: rgba(255, 255, 255, 0.95);
    border-color: var(--accent-gold);
}

.menu-item:hover::before {
    transform: scaleY(1);
}

.menu-item.featured {
    background: linear-gradient(135deg, rgba(201, 169, 97, 0.08), rgba(232, 213, 163, 0.08));
    border-color: var(--primary-gold);
}

.menu-item.featured::before {
    transform: scaleY(1);
}

.item-info {
    flex: 1;
    margin-right: var(--spacing-lg);
}

.item-name {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--deep-brown);
    margin-bottom: var(--spacing-sm);
    letter-spacing: 0.5px;
}

.item-description {
    font-size: 1rem;
    color: var(--text-medium);
    font-style: italic;
    line-height: 1.6;
    font-weight: 300;
}

.item-price {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--primary-gold);
    white-space: nowrap;
    text-shadow: 0 1px 2px var(--shadow-soft);
    position: relative;
    font-family: 'Inter', sans-serif;
}

/* Image Blocks */
.image-block {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.image-placeholder {
    width: 100%;
    height: 300px;
    background: linear-gradient(135deg, var(--soft-beige), var(--warm-cream));
    border: 3px solid var(--primary-gold);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-medium);
    font-style: italic;
    font-size: 1.1rem;
    font-weight: 300;
    box-shadow:
        0 8px 32px var(--shadow-soft),
        inset 0 1px 0 rgba(255, 255, 255, 0.5);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.image-placeholder::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(201, 169, 97, 0.1), transparent);
    transform: rotate(45deg);
    transition: transform 0.6s ease;
}

.image-placeholder:hover {
    transform: scale(1.03) rotate(1deg);
    box-shadow:
        0 16px 48px var(--shadow-medium),
        inset 0 1px 0 rgba(255, 255, 255, 0.7);
}

.image-placeholder:hover::before {
    transform: rotate(45deg) translate(50%, 50%);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .section-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .section-content:nth-child(even) {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .menu-container {
        margin: var(--spacing-sm);
        padding: var(--spacing-lg);
    }

    .hotel-name {
        font-size: 2.8rem;
        letter-spacing: 1px;
    }

    .section-title {
        font-size: 2.2rem;
    }

    .section-title::before,
    .section-title::after {
        display: none;
    }

    .menu-section {
        padding: var(--spacing-lg) var(--spacing-md);
    }

    .menu-item {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
    }

    .item-info {
        margin-right: 0;
    }

    .item-price {
        align-self: flex-end;
        font-size: 1.3rem;
    }

    .image-placeholder {
        height: 220px;
    }
}

@media (max-width: 480px) {
    .menu-container {
        margin: 0;
        border-radius: 0;
        padding: var(--spacing-md);
    }

    .hotel-name {
        font-size: 2.2rem;
        letter-spacing: 0.5px;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .menu-item {
        padding: var(--spacing-sm);
    }

    .item-name {
        font-size: 1.3rem;
    }

    .item-description {
        font-size: 0.9rem;
    }

    .item-price {
        font-size: 1.2rem;
    }

    .image-placeholder {
        height: 180px;
    }

    .decorative-divider {
        width: 100px;
    }
}

/* Print Styles */
@media print {
    body {
        background: white;
    }
    
    body::before {
        display: none;
    }
    
    .menu-container {
        box-shadow: none;
        margin: 0;
    }
    
    .menu-item:hover {
        transform: none;
        box-shadow: 0 2px 8px var(--shadow-light);
    }
}
