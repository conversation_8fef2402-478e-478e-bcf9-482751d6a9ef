/* CSS Variables for Consistent Theming */
:root {
    --primary-gold: #D4AF37;
    --light-gold: #F4E4BC;
    --dark-gold: #B8941F;
    --white: #ffffff;
    --light-grey: #f8f8f8;
    --text-dark: #333333;
    --text-light: #666666;
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-medium: rgba(0, 0, 0, 0.15);
    --border-radius: 8px;
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Lato', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--white);
    background-image: 
        radial-gradient(circle at 25% 25%, var(--light-gold) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, var(--light-gold) 1px, transparent 1px);
    background-size: 50px 50px;
    background-position: 0 0, 25px 25px;
    opacity: 0.95;
}

/* Moroccan Geometric Pattern Overlay */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(45deg, transparent 40%, var(--light-gold) 40%, var(--light-gold) 60%, transparent 60%),
        linear-gradient(-45deg, transparent 40%, var(--light-gold) 40%, var(--light-gold) 60%, transparent 60%);
    background-size: 100px 100px;
    opacity: 0.03;
    pointer-events: none;
    z-index: -1;
}

/* Main Container */
.menu-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    background-color: var(--white);
    box-shadow: 0 0 30px var(--shadow-light);
    border-radius: var(--border-radius);
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

/* Header Styles */
.menu-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--primary-gold);
    position: relative;
}

.logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
}

.hotel-logo {
    width: 120px;
    height: auto;
    max-height: 120px;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px var(--shadow-light));
    transition: transform 0.3s ease;
}

.logo-placeholder:hover .hotel-logo {
    transform: scale(1.05);
}

.hotel-name {
    font-family: 'Almendra Display', serif;
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-gold);
    text-shadow: 2px 2px 4px var(--shadow-light);
    margin: 0;
}

.hotel-subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
    font-style: italic;
    font-weight: 300;
}

/* Section Styles */
.menu-section {
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg) 0;
}

.menu-section:not(:last-child) {
    border-bottom: 1px solid var(--light-gold);
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.section-title {
    font-family: 'Almendra Display', serif;
    font-size: 2.5rem;
    color: var(--primary-gold);
    margin-bottom: var(--spacing-sm);
    position: relative;
}

.decorative-divider {
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, transparent, var(--primary-gold), transparent);
    margin: 0 auto;
    position: relative;
}

.decorative-divider::before,
.decorative-divider::after {
    content: '';
    position: absolute;
    top: -2px;
    width: 8px;
    height: 8px;
    background-color: var(--primary-gold);
    border-radius: 50%;
}

.decorative-divider::before {
    left: -4px;
}

.decorative-divider::after {
    right: -4px;
}

/* Section Content Layout */
.section-content {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: var(--spacing-lg);
    align-items: start;
}

.section-content:nth-child(even) {
    grid-template-columns: 300px 1fr;
}

/* Menu Items */
.menu-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.menu-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: var(--spacing-md);
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 8px var(--shadow-light);
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.menu-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px var(--shadow-medium);
    border-left-color: var(--primary-gold);
    background-color: rgba(244, 228, 188, 0.1);
}

.menu-item.featured {
    border-left-color: var(--primary-gold);
    background-color: rgba(244, 228, 188, 0.05);
}

.item-info {
    flex: 1;
    margin-right: var(--spacing-md);
}

.item-name {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: var(--spacing-xs);
}

.item-description {
    font-size: 0.95rem;
    color: var(--text-light);
    font-style: italic;
    line-height: 1.4;
}

.item-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-gold);
    white-space: nowrap;
}

/* Image Blocks */
.image-block {
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-placeholder {
    width: 100%;
    height: 250px;
    background: linear-gradient(135deg, var(--light-grey), var(--white));
    border: 2px solid var(--primary-gold);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    font-style: italic;
    box-shadow: 0 4px 12px var(--shadow-light);
    transition: transform 0.3s ease;
}

.image-placeholder:hover {
    transform: scale(1.02);
}

/* Responsive Design */
@media (max-width: 768px) {
    .menu-container {
        margin: var(--spacing-sm);
        padding: var(--spacing-md);
    }
    
    .hotel-name {
        font-size: 2.2rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .section-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .section-content:nth-child(even) {
        grid-template-columns: 1fr;
    }
    
    .menu-item {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
    }
    
    .item-info {
        margin-right: 0;
    }
    
    .item-price {
        align-self: flex-end;
        font-size: 1.4rem;
    }
    
    .image-placeholder {
        height: 200px;
    }
}

@media (max-width: 480px) {
    .hotel-name {
        font-size: 1.8rem;
    }
    
    .section-title {
        font-size: 1.6rem;
    }
    
    .menu-container {
        margin: 0;
        border-radius: 0;
    }
    
    .image-placeholder {
        height: 150px;
    }
}

/* Print Styles */
@media print {
    body {
        background: white;
    }
    
    body::before {
        display: none;
    }
    
    .menu-container {
        box-shadow: none;
        margin: 0;
    }
    
    .menu-item:hover {
        transform: none;
        box-shadow: 0 2px 8px var(--shadow-light);
    }
}
