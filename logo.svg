<svg width="140" height="140" viewBox="0 0 140 140" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Outer decorative circle -->
  <circle cx="70" cy="70" r="65" stroke="url(#goldGradient)" stroke-width="2" fill="none" opacity="0.6"/>

  <!-- Inner decorative elements -->
  <g stroke="url(#goldGradient)" stroke-width="1.5" opacity="0.4">
    <circle cx="70" cy="70" r="50" fill="none"/>
    <circle cx="70" cy="70" r="35" fill="none"/>
  </g>

  <!-- Moroccan star pattern -->
  <g fill="url(#goldGradient)" opacity="0.3">
    <polygon points="70,25 72,32 79,30 75,37 82,39 75,43 79,50 72,48 70,55 68,48 61,50 65,43 58,39 65,37 61,30 68,32"/>
    <polygon points="70,85 72,92 79,90 75,97 82,99 75,103 79,110 72,108 70,115 68,108 61,110 65,103 58,99 65,97 61,90 68,92"/>
    <polygon points="25,70 32,72 30,79 37,75 39,82 43,75 50,79 48,72 55,70 48,68 50,61 43,65 39,58 37,65 30,61 32,68"/>
    <polygon points="85,70 92,72 90,79 97,75 99,82 103,75 110,79 108,72 115,70 108,68 110,61 103,65 99,58 97,65 90,61 92,68"/>
  </g>

  <!-- Central emblem -->
  <circle cx="70" cy="70" r="25" fill="url(#goldGradient)" opacity="0.1"/>

  <!-- Letter R - elegant design -->
  <g fill="url(#darkGoldGradient)">
    <path d="M55 55 L55 85 L60 85 L60 72 L70 72 L70 67 L60 67 L60 60 L72 60 Q77 60 77 65 Q77 70 72 70 L68 70 L75 85 L81 85 L74 70 Q82 70 82 62 Q82 55 75 55 Z"/>
  </g>

  <!-- Decorative script "amy" -->
  <g fill="#2C1810" opacity="0.8">
    <path d="M78 65 Q80 62 83 64 Q86 67 88 64 Q90 62 93 67 Q95 72 98 67"
          stroke="#2C1810" stroke-width="1.5" fill="none" stroke-linecap="round"/>
    <circle cx="80" cy="70" r="1"/>
    <circle cx="90" cy="70" r="1"/>
    <circle cx="96" cy="70" r="1"/>
  </g>

  <!-- Hotel text -->
  <text x="70" y="105" text-anchor="middle" font-family="serif" font-size="11" font-weight="600" fill="url(#goldGradient)" letter-spacing="2px">HOTEL</text>

  <!-- Gradient definitions -->
  <defs>
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#C9A961;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#E8D5A3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A08B47;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="darkGoldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#A08B47;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#C9A961;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
